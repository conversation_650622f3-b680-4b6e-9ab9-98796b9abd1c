#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تنبيهات المخزون
يقوم بمراقبة كميات المخزون وإرسال تنبيهات عند اقتراب الكميات من النفاذ
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QDialog, QFormLayout, QDoubleSpinBox,
                            QComboBox, QTextEdit, QCheckBox, QGroupBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QIcon
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta
from database import OpeningBalance, Sale, Purchase, InventoryAlert, User
from db_session import session_scope
from logger import log_info, log_error

class InventoryAlertSystem:
    """نظام تنبيهات المخزون"""
    
    def __init__(self):
        self.engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def calculate_current_inventory(self):
        """حساب المخزون الحالي لكل نوع ألماس"""
        try:
            inventory_data = {}
            
            # الحصول على جميع أنواع الألماس من الأرصدة الافتتاحية
            opening_balances = self.session.query(OpeningBalance).all()
            
            for balance in opening_balances:
                diamond_type = balance.diamond_type
                
                # الرصيد الافتتاحي
                opening_quantity = balance.quantity
                minimum_stock = balance.minimum_stock
                
                # إجمالي المشتريات
                total_purchases = self.session.query(func.sum(Purchase.carat_weight)).filter(
                    Purchase.diamond_type == diamond_type
                ).scalar() or 0
                
                # إجمالي المبيعات
                total_sales = self.session.query(func.sum(Sale.carat_weight)).filter(
                    Sale.diamond_type == diamond_type
                ).scalar() or 0
                
                # الكمية الحالية = الرصيد الافتتاحي + المشتريات - المبيعات
                current_quantity = opening_quantity + total_purchases - total_sales
                
                inventory_data[diamond_type] = {
                    'current_quantity': current_quantity,
                    'minimum_stock': minimum_stock,
                    'opening_quantity': opening_quantity,
                    'total_purchases': total_purchases,
                    'total_sales': total_sales
                }
            
            return inventory_data
            
        except Exception as e:
            log_error(f"خطأ في حساب المخزون الحالي: {str(e)}", e)
            return {}
    
    def check_inventory_levels(self):
        """فحص مستويات المخزون وإنشاء التنبيهات"""
        try:
            inventory_data = self.calculate_current_inventory()
            alerts_created = []
            
            for diamond_type, data in inventory_data.items():
                current_qty = data['current_quantity']
                min_stock = data['minimum_stock']
                
                # تحديد مستوى التنبيه
                alert_level = None
                alert_message = ""
                
                if current_qty <= 0:
                    alert_level = "critical"
                    alert_message = f"نفد المخزون من {diamond_type} تماماً! الكمية الحالية: {current_qty:.3f} قيراط"
                elif current_qty <= min_stock * 0.5:
                    alert_level = "critical"
                    alert_message = f"المخزون من {diamond_type} في مستوى حرج! الكمية الحالية: {current_qty:.3f} قيراط، الحد الأدنى: {min_stock:.3f} قيراط"
                elif current_qty <= min_stock:
                    alert_level = "warning"
                    alert_message = f"المخزون من {diamond_type} أقل من الحد الأدنى! الكمية الحالية: {current_qty:.3f} قيراط، الحد الأدنى: {min_stock:.3f} قيراط"
                elif current_qty <= min_stock * 1.5:
                    alert_level = "low"
                    alert_message = f"المخزون من {diamond_type} يقترب من النفاذ. الكمية الحالية: {current_qty:.3f} قيراط، الحد الأدنى: {min_stock:.3f} قيراط"
                
                # إنشاء التنبيه إذا لزم الأمر
                if alert_level:
                    # التحقق من وجود تنبيه نشط مماثل
                    existing_alert = self.session.query(InventoryAlert).filter(
                        InventoryAlert.diamond_type == diamond_type,
                        InventoryAlert.alert_level == alert_level,
                        InventoryAlert.is_active == True
                    ).first()
                    
                    if not existing_alert:
                        # إنشاء تنبيه جديد
                        new_alert = InventoryAlert(
                            diamond_type=diamond_type,
                            current_quantity=current_qty,
                            minimum_stock=min_stock,
                            alert_level=alert_level,
                            alert_message=alert_message,
                            is_active=True,
                            created_date=datetime.now()
                        )
                        self.session.add(new_alert)
                        alerts_created.append(new_alert)
                    else:
                        # تحديث التنبيه الموجود
                        existing_alert.current_quantity = current_qty
                        existing_alert.alert_message = alert_message
                        existing_alert.created_date = datetime.now()
            
            # حفظ التغييرات
            self.session.commit()
            
            log_info(f"تم فحص المخزون وإنشاء {len(alerts_created)} تنبيه جديد")
            return alerts_created
            
        except Exception as e:
            self.session.rollback()
            log_error(f"خطأ في فحص مستويات المخزون: {str(e)}", e)
            return []
    
    def get_active_alerts(self):
        """الحصول على التنبيهات النشطة"""
        try:
            alerts = self.session.query(InventoryAlert).filter(
                InventoryAlert.is_active == True
            ).order_by(
                InventoryAlert.alert_level.desc(),
                InventoryAlert.created_date.desc()
            ).all()
            
            return alerts
            
        except Exception as e:
            log_error(f"خطأ في الحصول على التنبيهات النشطة: {str(e)}", e)
            return []
    
    def acknowledge_alert(self, alert_id, user_id):
        """الاطلاع على التنبيه"""
        try:
            alert = self.session.query(InventoryAlert).filter(
                InventoryAlert.id == alert_id
            ).first()
            
            if alert:
                alert.acknowledged_date = datetime.now()
                alert.acknowledged_by = user_id
                alert.is_active = False
                self.session.commit()
                
                log_info(f"تم الاطلاع على التنبيه {alert_id} من قبل المستخدم {user_id}")
                return True
            
            return False
            
        except Exception as e:
            self.session.rollback()
            log_error(f"خطأ في الاطلاع على التنبيه: {str(e)}", e)
            return False
    
    def update_minimum_stock(self, diamond_type, new_minimum):
        """تحديث الحد الأدنى للمخزون"""
        try:
            balance = self.session.query(OpeningBalance).filter(
                OpeningBalance.diamond_type == diamond_type
            ).first()
            
            if balance:
                balance.minimum_stock = new_minimum
                self.session.commit()
                
                log_info(f"تم تحديث الحد الأدنى للمخزون لـ {diamond_type} إلى {new_minimum}")
                return True
            
            return False
            
        except Exception as e:
            self.session.rollback()
            log_error(f"خطأ في تحديث الحد الأدنى للمخزون: {str(e)}", e)
            return False
    
    def close(self):
        """إغلاق الجلسة"""
        if self.session:
            self.session.close()

class InventoryAlertsWidget(QWidget):
    """واجهة تنبيهات المخزون"""
    
    alert_acknowledged = pyqtSignal(int)  # إشارة عند الاطلاع على تنبيه
    
    def __init__(self, user=None):
        super().__init__()
        self.user = user
        self.alert_system = InventoryAlertSystem()
        self.init_ui()
        self.load_alerts()
        
        # تحديث التنبيهات كل 5 دقائق
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_alerts)
        self.timer.start(300000)  # 5 دقائق
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("تنبيهات المخزون")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.clicked.connect(self.refresh_alerts)
        buttons_layout.addWidget(self.refresh_btn)
        
        self.check_inventory_btn = QPushButton("فحص المخزون")
        self.check_inventory_btn.clicked.connect(self.check_inventory)
        buttons_layout.addWidget(self.check_inventory_btn)
        
        self.settings_btn = QPushButton("إعدادات المخزون")
        self.settings_btn.clicked.connect(self.open_inventory_settings)
        buttons_layout.addWidget(self.settings_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول التنبيهات
        self.alerts_table = QTableWidget()
        self.alerts_table.setColumnCount(6)
        self.alerts_table.setHorizontalHeaderLabels([
            "نوع الألماس", "الكمية الحالية", "الحد الأدنى", 
            "مستوى التنبيه", "الرسالة", "التاريخ"
        ])
        
        header = self.alerts_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(self.alerts_table)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.acknowledge_btn = QPushButton("تم الاطلاع")
        self.acknowledge_btn.clicked.connect(self.acknowledge_selected_alert)
        self.acknowledge_btn.setEnabled(False)
        actions_layout.addWidget(self.acknowledge_btn)
        
        self.acknowledge_all_btn = QPushButton("تم الاطلاع على الكل")
        self.acknowledge_all_btn.clicked.connect(self.acknowledge_all_alerts)
        actions_layout.addWidget(self.acknowledge_all_btn)
        
        layout.addLayout(actions_layout)
        
        # ربط إشارة تحديد الصف
        self.alerts_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        self.setLayout(layout)
    
    def load_alerts(self):
        """تحميل التنبيهات"""
        try:
            alerts = self.alert_system.get_active_alerts()
            
            self.alerts_table.setRowCount(len(alerts))
            
            for row, alert in enumerate(alerts):
                # نوع الألماس
                self.alerts_table.setItem(row, 0, QTableWidgetItem(alert.diamond_type))
                
                # الكمية الحالية
                qty_item = QTableWidgetItem(f"{alert.current_quantity:.3f} قيراط")
                self.alerts_table.setItem(row, 1, qty_item)
                
                # الحد الأدنى
                min_item = QTableWidgetItem(f"{alert.minimum_stock:.3f} قيراط")
                self.alerts_table.setItem(row, 2, min_item)
                
                # مستوى التنبيه
                level_item = QTableWidgetItem(self.get_alert_level_text(alert.alert_level))
                level_item.setBackground(self.get_alert_color(alert.alert_level))
                self.alerts_table.setItem(row, 3, level_item)
                
                # الرسالة
                self.alerts_table.setItem(row, 4, QTableWidgetItem(alert.alert_message))
                
                # التاريخ
                date_str = alert.created_date.strftime("%Y-%m-%d %H:%M")
                self.alerts_table.setItem(row, 5, QTableWidgetItem(date_str))
                
                # حفظ معرف التنبيه
                self.alerts_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, alert.id)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل التنبيهات: {str(e)}")
    
    def get_alert_level_text(self, level):
        """الحصول على نص مستوى التنبيه"""
        levels = {
            'critical': 'حرج',
            'warning': 'تحذير',
            'low': 'منخفض'
        }
        return levels.get(level, level)
    
    def get_alert_color(self, level):
        """الحصول على لون مستوى التنبيه"""
        colors = {
            'critical': QColor(255, 200, 200),  # أحمر فاتح
            'warning': QColor(255, 255, 200),   # أصفر فاتح
            'low': QColor(200, 255, 200)        # أخضر فاتح
        }
        return colors.get(level, QColor(255, 255, 255))
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        selected_rows = self.alerts_table.selectionModel().selectedRows()
        self.acknowledge_btn.setEnabled(len(selected_rows) > 0)
    
    def refresh_alerts(self):
        """تحديث التنبيهات"""
        self.load_alerts()
    
    def check_inventory(self):
        """فحص المخزون وإنشاء تنبيهات جديدة"""
        try:
            new_alerts = self.alert_system.check_inventory_levels()
            
            if new_alerts:
                QMessageBox.information(
                    self, 
                    "تم الفحص", 
                    f"تم إنشاء {len(new_alerts)} تنبيه جديد"
                )
            else:
                QMessageBox.information(
                    self, 
                    "تم الفحص", 
                    "لا توجد تنبيهات جديدة"
                )
            
            self.load_alerts()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فحص المخزون: {str(e)}")
    
    def acknowledge_selected_alert(self):
        """الاطلاع على التنبيه المحدد"""
        try:
            current_row = self.alerts_table.currentRow()
            if current_row >= 0:
                alert_id = self.alerts_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
                user_id = self.user.id if self.user else None
                
                if self.alert_system.acknowledge_alert(alert_id, user_id):
                    self.alert_acknowledged.emit(alert_id)
                    self.load_alerts()
                    QMessageBox.information(self, "تم", "تم الاطلاع على التنبيه")
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في الاطلاع على التنبيه")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def acknowledge_all_alerts(self):
        """الاطلاع على جميع التنبيهات"""
        try:
            alerts = self.alert_system.get_active_alerts()
            user_id = self.user.id if self.user else None
            
            acknowledged_count = 0
            for alert in alerts:
                if self.alert_system.acknowledge_alert(alert.id, user_id):
                    acknowledged_count += 1
                    self.alert_acknowledged.emit(alert.id)
            
            self.load_alerts()
            QMessageBox.information(
                self, 
                "تم", 
                f"تم الاطلاع على {acknowledged_count} تنبيه"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")
    
    def open_inventory_settings(self):
        """فتح إعدادات المخزون"""
        dialog = InventorySettingsDialog(self.alert_system, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.check_inventory()
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        self.alert_system.close()
        event.accept()

class InventorySettingsDialog(QDialog):
    """حوار إعدادات المخزون"""
    
    def __init__(self, alert_system, parent=None):
        super().__init__(parent)
        self.alert_system = alert_system
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إعدادات المخزون")
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout()
        
        # جدول إعدادات المخزون
        self.settings_table = QTableWidget()
        self.settings_table.setColumnCount(4)
        self.settings_table.setHorizontalHeaderLabels([
            "نوع الألماس", "الكمية الحالية", "الحد الأدنى الحالي", "الحد الأدنى الجديد"
        ])
        
        header = self.settings_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(self.settings_table)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            inventory_data = self.alert_system.calculate_current_inventory()
            
            self.settings_table.setRowCount(len(inventory_data))
            
            for row, (diamond_type, data) in enumerate(inventory_data.items()):
                # نوع الألماس
                self.settings_table.setItem(row, 0, QTableWidgetItem(diamond_type))
                
                # الكمية الحالية
                current_qty = f"{data['current_quantity']:.3f} قيراط"
                self.settings_table.setItem(row, 1, QTableWidgetItem(current_qty))
                
                # الحد الأدنى الحالي
                current_min = f"{data['minimum_stock']:.3f} قيراط"
                self.settings_table.setItem(row, 2, QTableWidgetItem(current_min))
                
                # الحد الأدنى الجديد (قابل للتعديل)
                new_min_spinbox = QDoubleSpinBox()
                new_min_spinbox.setRange(0.001, 999.999)
                new_min_spinbox.setDecimals(3)
                new_min_spinbox.setValue(data['minimum_stock'])
                new_min_spinbox.setSuffix(" قيراط")
                
                self.settings_table.setCellWidget(row, 3, new_min_spinbox)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            updated_count = 0
            
            for row in range(self.settings_table.rowCount()):
                diamond_type = self.settings_table.item(row, 0).text()
                new_min_spinbox = self.settings_table.cellWidget(row, 3)
                new_minimum = new_min_spinbox.value()
                
                if self.alert_system.update_minimum_stock(diamond_type, new_minimum):
                    updated_count += 1
            
            QMessageBox.information(
                self, 
                "تم", 
                f"تم تحديث {updated_count} عنصر"
            )
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # اختبار النظام
    alert_system = InventoryAlertSystem()
    alerts = alert_system.check_inventory_levels()
    
    print(f"تم إنشاء {len(alerts)} تنبيه")
    
    # عرض واجهة التنبيهات
    widget = InventoryAlertsWidget()
    widget.show()
    
    sys.exit(app.exec())
