#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير Excel للتقارير المحدثة
"""

import xlsxwriter
from datetime import datetime

def create_test_customer_excel():
    """إنشاء ملف Excel تجريبي لتقرير العميل بالتنسيق الجديد"""
    
    workbook = xlsxwriter.Workbook('test_customer_report_excel.xlsx')
    worksheet = workbook.add_worksheet("تقرير العميل")

    # Enhanced formatting
    title_format = workbook.add_format({
        'bold': True,
        'font_size': 16,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': '#1F497D',
        'bg_color': '#E6EFF9',
        'border': 1,
        'text_wrap': True
    })

    header_format = workbook.add_format({
        'bold': True,
        'font_size': 12,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#BDD7EE',
        'border': 1,
        'text_wrap': True,
        'border_color': '#4472C4'
    })

    cell_format = workbook.add_format({
        'border': 1,
        'align': 'center',
        'valign': 'vcenter',
        'border_color': '#B4C6E7',
        'text_wrap': True
    })

    # Summary formats for the new horizontal layout
    summary_header_format = workbook.add_format({
        'bold': True,
        'font_size': 14,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': '#FFFFFF',
        'bg_color': '#2C3E50',
        'border': 1,
        'text_wrap': True
    })

    summary_label_format = workbook.add_format({
        'bold': True,
        'font_size': 11,
        'align': 'right',
        'valign': 'vcenter',
        'bg_color': '#F8F9FA',
        'border': 1,
        'text_wrap': True
    })

    summary_value_format = workbook.add_format({
        'font_size': 11,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#FFFFFF',
        'border': 1,
        'num_format': '#,##0.00'
    })

    num_format = workbook.add_format({
        'border': 1,
        'align': 'center',
        'valign': 'vcenter',
        'num_format': '#,##0.00',
        'border_color': '#B4C6E7'
    })

    total_format = workbook.add_format({
        'bold': True,
        'border': 1,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#D9E2F3',
        'num_format': '#,##0.00',
        'border_color': '#4472C4'
    })

    # Set column widths
    worksheet.set_column('A:A', 15)  # Date
    worksheet.set_column('B:B', 18)  # Diamond Type
    worksheet.set_column('C:L', 15)  # Values

    # RTL direction
    worksheet.right_to_left()

    # Add title and info
    worksheet.merge_range('A1:J1', "نظام إدارة مبيعات الألماس", title_format)
    worksheet.merge_range('A2:J2', "تقرير العميل: أحمد محمد", title_format)
    worksheet.merge_range('A3:J3', "للفترة من 2024-01-01 إلى 2024-12-31", title_format)

    # Headers
    headers = ['التاريخ', 'نوع الألماس', 'الوزن', 'سعر القيراط ($)', 'السعر بالريال للكمية',
              'المبلغ المسدد ($)', 'المبلغ المسدد (ريال)', 'الرصيد ($)', 'الرصيد (ريال)', 'ملاحظات']
    for col, header in enumerate(headers):
        worksheet.write(4, col, header, header_format)

    # Sample data
    data = [
        ['2024-01-15', 'ألماس طبيعي', 2.50, 1000.00, 9375.00, 1500.00, 5625.00, 1000.00, 3750.00, 'دفعة أولى'],
        ['2024-01-20', 'سند قبض', 0, 0, 0, 500.00, 1875.00, 500.00, 1875.00, 'دفعة على الحساب'],
        ['2024-02-10', 'ألماس صناعي', 1.75, 800.00, 5250.00, 800.00, 3000.00, 1100.00, 4125.00, 'سداد كامل']
    ]

    row = 5
    for data_row in data:
        for col, value in enumerate(data_row):
            if col in [2, 3, 4, 5, 6, 7, 8]:  # Numeric columns
                worksheet.write(row, col, value, num_format)
            else:
                worksheet.write(row, col, value, cell_format)
        row += 1

    # Totals row
    worksheet.write(row, 0, "الإجمالي", total_format)
    worksheet.write(row, 1, "", total_format)
    worksheet.write(row, 2, 4.25, total_format)
    worksheet.write(row, 3, 900.00, total_format)
    worksheet.write(row, 4, 14625.00, total_format)
    worksheet.write(row, 5, 2800.00, total_format)
    worksheet.write(row, 6, 10500.00, total_format)
    worksheet.write(row, 7, 1100.00, total_format)
    worksheet.write(row, 8, 4125.00, total_format)
    worksheet.write(row, 9, "", total_format)

    # إضافة ملخص الإجماليات بالتنسيق الأفقي الجديد
    row += 3  # ترك مساحة فارغة
    
    # عنوان ملخص الإجماليات
    worksheet.merge_range(f'A{row}:J{row}', "ملخص الإجماليات", summary_header_format)
    row += 1
    
    # الصف الأول: إجمالي الوزن | إجمالي المستحق
    worksheet.write(row, 0, "إجمالي الوزن:", summary_label_format)
    worksheet.write(row, 1, "4.25 قيراط", summary_value_format)
    worksheet.write(row, 2, "", summary_value_format)  # فاصل
    worksheet.write(row, 3, "إجمالي المستحق:", summary_label_format)
    worksheet.write(row, 4, "$ 3,825.00", summary_value_format)
    worksheet.write(row, 5, "|", summary_value_format)
    worksheet.write(row, 6, "14,343.75 ريال", summary_value_format)
    # ملء باقي الخلايا
    for col in range(7, 10):
        worksheet.write(row, col, "", summary_value_format)
    row += 1
    
    # الصف الثاني: إجمالي المسدد | إجمالي الرصيد
    worksheet.write(row, 0, "إجمالي المسدد:", summary_label_format)
    worksheet.write(row, 1, "$ 2,800.00", summary_value_format)
    worksheet.write(row, 2, "10,500.00 ريال", summary_value_format)
    worksheet.write(row, 3, "إجمالي الرصيد:", summary_label_format)
    worksheet.write(row, 4, "$ 1,100.00", summary_value_format)
    worksheet.write(row, 5, "|", summary_value_format)
    worksheet.write(row, 6, "4,125.00 ريال", summary_value_format)
    # ملء باقي الخلايا
    for col in range(7, 10):
        worksheet.write(row, col, "", summary_value_format)

    # Footer
    worksheet.merge_range(f'A{row+3}:J{row+3}',
                        f"تم إنشاء هذا التقرير بواسطة: المدير",
                        title_format)
    worksheet.merge_range(f'A{row+4}:J{row+4}',
                        f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        title_format)

    workbook.close()
    print("تم إنشاء ملف test_customer_report_excel.xlsx بنجاح!")

if __name__ == "__main__":
    create_test_customer_excel()
