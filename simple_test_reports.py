#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار بسيط لتقارير السندات
"""

import sys
import os

# إضافة المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database import *
    print("تم استيراد database بنجاح")
    
    # إنشاء جلسة
    session = get_db_session()
    print("تم إنشاء جلسة قاعدة البيانات")
    
    # فحص السندات
    vouchers_count = session.query(Voucher).count()
    print(f"عدد السندات في قاعدة البيانات: {vouchers_count}")
    
    # فحص العملاء
    customers_count = session.query(Customer).count()
    print(f"عدد العملاء: {customers_count}")
    
    # فحص الموردين
    suppliers_count = session.query(Supplier).count()
    print(f"عدد الموردين: {suppliers_count}")
    
    session.close()
    print("تم إغلاق الجلسة بنجاح")
    
    # اختبار استيراد ReportsScreen
    try:
        import tkinter as tk
        from reports_screen import ReportsScreen
        print("تم استيراد ReportsScreen بنجاح")
        
        # إنشاء root window
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء session جديدة
        session = get_db_session()
        
        # إنشاء كائن ReportsScreen
        reports = ReportsScreen(root, session)
        print("تم إنشاء كائن ReportsScreen بنجاح")
        
        # تنظيف
        session.close()
        root.destroy()
        
    except Exception as e:
        print(f"خطأ في استيراد أو إنشاء ReportsScreen: {e}")
        import traceback
        traceback.print_exc()
    
except Exception as e:
    print(f"خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()

print("انتهى الاختبار")
