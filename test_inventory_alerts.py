#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تنبيهات المخزون
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QMessageBox
from PyQt6.QtCore import Qt
from inventory_alerts import InventoryAlertSystem, InventoryAlertsWidget
from database import OpeningBalance, InventoryAlert
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

class TestInventoryAlertsWindow(QMainWindow):
    """نافذة اختبار نظام تنبيهات المخزون"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_test_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("اختبار نظام تنبيهات المخزون")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("اختبار نظام تنبيهات المخزون")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # أزرار الاختبار
        self.create_test_data_btn = QPushButton("إنشاء بيانات اختبار")
        self.create_test_data_btn.clicked.connect(self.create_test_data)
        self.create_test_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(self.create_test_data_btn)
        
        self.check_inventory_btn = QPushButton("فحص المخزون")
        self.check_inventory_btn.clicked.connect(self.check_inventory)
        self.check_inventory_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(self.check_inventory_btn)
        
        self.show_alerts_btn = QPushButton("عرض التنبيهات")
        self.show_alerts_btn.clicked.connect(self.show_alerts)
        self.show_alerts_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        layout.addWidget(self.show_alerts_btn)
        
        self.clear_alerts_btn = QPushButton("مسح جميع التنبيهات")
        self.clear_alerts_btn.clicked.connect(self.clear_alerts)
        self.clear_alerts_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        layout.addWidget(self.clear_alerts_btn)
        
        # معلومات الحالة
        self.status_label = QLabel("جاهز للاختبار")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
                border: 1px solid #27ae60;
                margin: 10px;
            }
        """)
        layout.addWidget(self.status_label)
        
        central_widget.setLayout(layout)
    
    def setup_test_data(self):
        """إعداد بيانات الاختبار"""
        try:
            self.engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=self.engine)
            self.session = Session()
            self.alert_system = InventoryAlertSystem()
            
            self.status_label.setText("تم إعداد نظام الاختبار بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إعداد نظام الاختبار: {str(e)}")
    
    def create_test_data(self):
        """إنشاء بيانات اختبار للمخزون"""
        try:
            # إنشاء أرصدة افتتاحية للاختبار
            test_data = [
                {"diamond_type": "ألماس طبيعي AAA", "quantity": 0.5, "minimum_stock": 2.0},  # حرج
                {"diamond_type": "ألماس طبيعي AA", "quantity": 1.0, "minimum_stock": 3.0},   # تحذير
                {"diamond_type": "ألماس طبيعي A", "quantity": 4.0, "minimum_stock": 3.0},    # منخفض
                {"diamond_type": "ألماس صناعي", "quantity": 10.0, "minimum_stock": 5.0},     # طبيعي
                {"diamond_type": "ألماس مختلط", "quantity": 0.0, "minimum_stock": 1.0},     # نفد تماماً
            ]
            
            for data in test_data:
                # التحقق من وجود الرصيد
                existing = self.session.query(OpeningBalance).filter(
                    OpeningBalance.diamond_type == data["diamond_type"]
                ).first()
                
                if existing:
                    # تحديث البيانات الموجودة
                    existing.quantity = data["quantity"]
                    existing.minimum_stock = data["minimum_stock"]
                else:
                    # إنشاء رصيد جديد
                    new_balance = OpeningBalance(
                        diamond_type=data["diamond_type"],
                        quantity=data["quantity"],
                        price_per_carat_usd=1000.0,
                        total_value_usd=data["quantity"] * 1000.0,
                        total_value_sar=data["quantity"] * 1000.0 * 3.75,
                        exchange_rate=3.75,
                        minimum_stock=data["minimum_stock"],
                        date_created=datetime.now()
                    )
                    self.session.add(new_balance)
            
            self.session.commit()
            
            QMessageBox.information(
                self, 
                "تم", 
                f"تم إنشاء {len(test_data)} عنصر اختبار للمخزون"
            )
            
            self.status_label.setText(f"تم إنشاء {len(test_data)} عنصر اختبار")
            
        except Exception as e:
            self.session.rollback()
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء بيانات الاختبار: {str(e)}")
    
    def check_inventory(self):
        """فحص المخزون وإنشاء التنبيهات"""
        try:
            if not hasattr(self, 'alert_system'):
                self.setup_test_data()
            
            # فحص المخزون
            new_alerts = self.alert_system.check_inventory_levels()
            
            # الحصول على جميع التنبيهات النشطة
            active_alerts = self.alert_system.get_active_alerts()
            
            # تصنيف التنبيهات حسب المستوى
            critical_count = len([a for a in active_alerts if a.alert_level == 'critical'])
            warning_count = len([a for a in active_alerts if a.alert_level == 'warning'])
            low_count = len([a for a in active_alerts if a.alert_level == 'low'])
            
            message = f"""
تم فحص المخزون:
• تنبيهات جديدة: {len(new_alerts)}
• إجمالي التنبيهات النشطة: {len(active_alerts)}
  - حرجة: {critical_count}
  - تحذيرية: {warning_count}
  - منخفضة: {low_count}
            """
            
            QMessageBox.information(self, "نتائج فحص المخزون", message)
            
            self.status_label.setText(f"تم فحص المخزون - {len(active_alerts)} تنبيه نشط")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فحص المخزون: {str(e)}")
    
    def show_alerts(self):
        """عرض شاشة التنبيهات"""
        try:
            if not hasattr(self, 'alert_system'):
                self.setup_test_data()
            
            # إنشاء وعرض شاشة التنبيهات
            self.alerts_widget = InventoryAlertsWidget()
            self.alerts_widget.show()
            
            self.status_label.setText("تم فتح شاشة التنبيهات")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض التنبيهات: {str(e)}")
    
    def clear_alerts(self):
        """مسح جميع التنبيهات"""
        try:
            if not hasattr(self, 'session'):
                self.setup_test_data()
            
            # مسح جميع التنبيهات
            deleted_count = self.session.query(InventoryAlert).delete()
            self.session.commit()
            
            QMessageBox.information(
                self, 
                "تم", 
                f"تم مسح {deleted_count} تنبيه"
            )
            
            self.status_label.setText(f"تم مسح {deleted_count} تنبيه")
            
        except Exception as e:
            self.session.rollback()
            QMessageBox.critical(self, "خطأ", f"فشل في مسح التنبيهات: {str(e)}")
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        try:
            if hasattr(self, 'alert_system'):
                self.alert_system.close()
            if hasattr(self, 'session'):
                self.session.close()
        except:
            pass
        event.accept()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تطبيق نمط عربي
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    # إنشاء وعرض النافذة
    window = TestInventoryAlertsWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
