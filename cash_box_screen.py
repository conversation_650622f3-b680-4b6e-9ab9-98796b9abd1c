from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QTableWidget, QTableWidgetItem, QDateEdit, QComboBox,
                            QLineEdit, QFormLayout, QMessageBox, QHeaderView, QGroupBox,
                            QDialog, QDoubleSpinBox, QTextEdit)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QColor, QFont
from datetime import datetime
from sqlalchemy import func, desc, or_, case, String, Float, Integer
from ui_utils import confirm_dialog
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية, Sale, Purchase, Receipt, Supplier, Customer
from permissions import check_permission
import re
import os

class حوار_حركة_نقدية(QDialog):
    """حوار لإضافة حركة نقدية جديدة"""

    def __init__(self, transaction_type="deposit", user=None): # Added user
        super().__init__()
        self.transaction_type = transaction_type
        self.user = user # Store user
        self.customer_combo = None
        self.supplier_combo = None
        self.init_ui()

    def init_ui(self):
        title = "إضافة إيداع نقدي" if self.transaction_type == "deposit" else "إضافة سحب نقدي"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(450, 420) # Adjusted size
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
            }
            QLabel {
                font-size: 10pt;
            }
            QLineEdit, QComboBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: white;
                font-size: 10pt;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 10pt;
                font-weight: bold;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)

        layout = QFormLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # العملة
        self.currency_combo = QComboBox()
        self.currency_combo.addItem("USD", "USD")
        self.currency_combo.addItem("SAR", "SAR")
        self.currency_combo.currentTextChanged.connect(self.update_amount_suffix)
        layout.addRow("العملة:", self.currency_combo)

        # المبلغ
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setMinimum(0.01)
        self.amount_spinbox.setMaximum(9999999.99)
        self.amount_spinbox.setDecimals(2)
        self.update_amount_suffix(self.currency_combo.currentData())
        layout.addRow("المبلغ:", self.amount_spinbox)
        
        # سعر الصرف (يظهر فقط عند اختيار الريال)
        self.exchange_rate_label = QLabel("سعر الصرف (مقابل الدولار):")
        self.exchange_rate_spinbox = QDoubleSpinBox()
        self.exchange_rate_spinbox.setMinimum(0.01)
        self.exchange_rate_spinbox.setMaximum(1000.00)
        self.exchange_rate_spinbox.setDecimals(4)
        self.exchange_rate_spinbox.setValue(3.75)
        self.exchange_rate_row_widget = QWidget()
        exchange_rate_layout = QHBoxLayout(self.exchange_rate_row_widget)
        exchange_rate_layout.addWidget(self.exchange_rate_label)
        exchange_rate_layout.addWidget(self.exchange_rate_spinbox)
        exchange_rate_layout.setContentsMargins(0,0,0,0)
        layout.addRow(self.exchange_rate_row_widget)
        self.exchange_rate_row_widget.setVisible(self.currency_combo.currentData() == "SAR")
        self.currency_combo.currentTextChanged.connect(lambda currency: self.exchange_rate_row_widget.setVisible(currency == "SAR"))

        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        layout.addRow("التاريخ:", self.date_edit)

        if self.transaction_type == "deposit":
            self.customer_combo = QComboBox()
            self.customer_combo.addItem("بدون عميل محدد", None)
            try:
                with session_scope() as session:
                    customers = session.query(Customer).all()
                    for customer in customers:
                        self.customer_combo.addItem(customer.name, customer.id)
            except Exception as e:
                print(f"Error loading customers: {e}")
            layout.addRow("العميل:", self.customer_combo)
        elif self.transaction_type == "withdraw":
            self.supplier_combo = QComboBox()
            self.supplier_combo.addItem("بدون مورد محدد", None)
            try:
                with session_scope() as session:
                    suppliers = session.query(Supplier).all()
                    for supplier in suppliers:
                        self.supplier_combo.addItem(supplier.name, supplier.id)
            except Exception as e:
                print(f"Error loading suppliers: {e}")
            layout.addRow("المورد:", self.supplier_combo)

        # المرجع
        self.reference_edit = QLineEdit()
        layout.addRow("المرجع:", self.reference_edit)

        # الوصف
        self.description_text = QTextEdit()
        self.description_text.setMaximumHeight(80) # Adjusted height
        layout.addRow("الوصف:", self.description_text)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.addStretch() # Push buttons to the right

        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.accept)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("background-color: #e74c3c;") # Red for cancel
        self.cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch() # Push buttons to the left

        layout.addRow(buttons_layout)
        self.setLayout(layout)

    def update_amount_suffix(self, currency_code):
        if currency_code == "USD":
            self.amount_spinbox.setSuffix(" $")
        elif currency_code == "SAR":
            self.amount_spinbox.setSuffix(" ريال")
        else:
            self.amount_spinbox.setSuffix("")

class شاشة_صندوق_النقدية(QWidget):
    refresh_signal = pyqtSignal()

    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_data()

    def init_ui(self):
        self.setWindowTitle("صندوق النقدية")
        self.setGeometry(100, 100, 1000, 600)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # مجموعة ملخص الصندوق
        summary_group = QGroupBox("ملخص الصندوق")
        summary_layout = QHBoxLayout()

        # ملصقات رصيد الصندوق
        self.total_balance_label = QLabel("الرصيد الحالي: 0.00 $")
        self.total_balance_label.setStyleSheet("font-size: 16px; font-weight: bold; color: green;")

        self.total_in_label = QLabel("إجمالي الإيداعات: 0.00 $")
        self.total_in_label.setStyleSheet("color: blue;")

        self.total_out_label = QLabel("إجمالي المسحوبات: 0.00 $")
        self.total_out_label.setStyleSheet("color: red;")

        summary_layout.addWidget(self.total_balance_label)
        summary_layout.addWidget(self.total_in_label)
        summary_layout.addWidget(self.total_out_label)
        summary_group.setLayout(summary_layout)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(summary_group)

        # مجموعة البحث
        search_group = QGroupBox("بحث")
        search_layout = QHBoxLayout()

        # تاريخ البدء
        start_date_label = QLabel("من تاريخ:")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-3))

        # تاريخ الانتهاء
        end_date_label = QLabel("إلى تاريخ:")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())

        # نوع الحركة
        transaction_type_label = QLabel("نوع الحركة:")
        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.addItem("الكل", "all")
        self.transaction_type_combo.addItem("إيداع", "deposit")
        self.transaction_type_combo.addItem("سحب", "withdraw")

        # زر البحث
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.search_transactions)

        # إضافة عناصر البحث إلى تخطيط البحث
        search_layout.addWidget(start_date_label)
        search_layout.addWidget(self.start_date_edit)
        search_layout.addWidget(end_date_label)
        search_layout.addWidget(self.end_date_edit)
        search_layout.addWidget(transaction_type_label)
        search_layout.addWidget(self.transaction_type_combo)
        search_layout.addWidget(self.search_button)
        search_group.setLayout(search_layout)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(search_group)

        # مجموعة أزرار الإجراءات
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QHBoxLayout()
          # زر إيداع نقدي
        self.deposit_button = QPushButton("إيداع نقدي")
        self.deposit_button.clicked.connect(self.add_deposit)

        # زر سحب نقدي
        self.withdraw_button = QPushButton("سحب نقدي")
        self.withdraw_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
                font-weight: bold;
                padding: 6px 18px;
            }
            QPushButton:hover {
                background-color: #b084cc;
            }
            QPushButton:pressed {
                background-color: #8e44ad;
            }
        """)
        self.print_button = QPushButton("طباعة التقرير")
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
                font-weight: bold;
                padding: 6px 18px;
            }
            QPushButton:hover {
                background-color: #1abc9c;
            }
            QPushButton:pressed {
                background-color: #117864;
            }
        """)
        self.print_button.clicked.connect(self.print_report)

        # إضافة أزرار الإجراءات إلى تخطيط الإجراءات
        actions_layout.addWidget(self.deposit_button)
        actions_layout.addWidget(self.withdraw_button)
        actions_layout.addWidget(self.print_button)

        # التحقق من صلاحيات المستخدم للتعديل
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            self.deposit_button.setEnabled(False)
            self.withdraw_button.setEnabled(False)

        actions_group.setLayout(actions_layout)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(actions_group)

        # جدول حركات الصندوق
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(10)
        self.transactions_table.setHorizontalHeaderLabels([
            "رقم", "التاريخ", "النوع", "مدين ($)", "دائن ($)",
            "الرصيد المتراكم ($)", "مدين (ريال)", "دائن (ريال)", "الرصيد المتراكم (ريال)", "الوصف"
        ])
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # إضافة إلى التخطيط الرئيسي
        main_layout.addWidget(self.transactions_table)

        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)

        # الاتصال بإشارة التحديث
        self.refresh_signal.connect(self.load_data)

    def load_data(self):
        """تحميل بيانات الصندوق وحركاته"""
        try:
            with session_scope() as session:
                # استعلام عن رصيد الصندوق
                cash_box = session.query(صندوق_النقدية).first()

                if not cash_box:
                    # إنشاء صندوق جديد إذا لم يكن موجوداً
                    cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                    session.add(cash_box)
                    session.commit()

                # تحديث ملصقات الملخص
                # حساب إجمالي الإيداعات والمسحوبات للفترة المحددة (للعرض فقط)
                start_date = datetime.combine(self.start_date_edit.date().toPyDate(), datetime.min.time())
                end_date = datetime.combine(self.end_date_edit.date().toPyDate(), datetime.max.time())

                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0

                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0

                # حساب الإيداعات من سندات القبض للفترة المحددة
                receipt_deposits = session.query(func.sum(Receipt.amount_usd)).filter(
                    Receipt.issue_date >= start_date,
                    Receipt.issue_date <= end_date,
                    or_(Receipt.receipt_type == "CashIn", Receipt.receipt_type == "قبض")
                ).scalar() or 0

                # حساب المسحوبات من فواتير المشتريات للفترة المحددة
                purchase_withdrawals = session.query(func.sum(Purchase.amount_paid)).filter(
                    Purchase.purchase_date >= start_date,
                    Purchase.purchase_date <= end_date,
                    Purchase.amount_paid > 0
                ).scalar() or 0

                # حساب المسحوبات من سندات الصرف للفترة المحددة
                receipt_withdrawals = session.query(func.sum(Receipt.amount_usd)).filter(
                    Receipt.issue_date >= start_date,
                    Receipt.issue_date <= end_date,
                    or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")
                ).scalar() or 0

                total_all_deposits = total_deposits + receipt_deposits
                total_all_withdrawals = total_withdrawals + purchase_withdrawals + receipt_withdrawals

                self.total_in_label.setText(f"إجمالي الإيداعات: {total_all_deposits:.2f} $")
                self.total_out_label.setText(f"إجمالي المسحوبات: {total_all_withdrawals:.2f} $")
                
                # حساب الرصيد الحالي الإجمالي (من جميع الحركات بدون قيود تاريخية)
                # حساب إجمالي الإيداعات من جميع الحركات النقدية
                total_all_deposits_ever = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0

                # حساب إجمالي المسحوبات من جميع الحركات النقدية
                total_all_withdrawals_ever = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0

                # حساب إجمالي الإيداعات من جميع سندات القبض
                total_receipt_deposits_ever = session.query(func.sum(Receipt.amount_usd)).filter(
                    or_(Receipt.receipt_type == "CashIn", Receipt.receipt_type == "قبض")
                ).scalar() or 0

                # حساب إجمالي المسحوبات من جميع فواتير المشتريات
                total_purchase_withdrawals_ever = session.query(func.sum(Purchase.amount_paid)).filter(
                    Purchase.amount_paid > 0
                ).scalar() or 0

                # حساب إجمالي المسحوبات من جميع سندات الصرف
                total_receipt_withdrawals_ever = session.query(func.sum(Receipt.amount_usd)).filter(
                    or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")
                ).scalar() or 0

                # حساب الرصيد الحالي الإجمالي
                current_balance = (total_all_deposits_ever + total_receipt_deposits_ever) - (total_all_withdrawals_ever + total_purchase_withdrawals_ever + total_receipt_withdrawals_ever)
                self.total_balance_label.setText(f"الرصيد الحالي: {current_balance:.2f} $")
                
                # تحديث رصيد الصندوق في قاعدة البيانات
                cash_box.balance = current_balance
                cash_box.last_updated = datetime.now()
                session.commit()

                # تحميل الحركات في الجدول
                self.load_transactions(session, start_date, end_date)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الصندوق: {str(e)}")

    def load_transactions(self, session, start_date, end_date, transaction_type="all"):
        """تحميل حركات الصندوق إلى الجدول"""
        try:
            # بناء الاستعلام للحركات النقدية المباشرة
            query = session.query(حركة_نقدية).filter(
                حركة_نقدية.transaction_date >= start_date,
                حركة_نقدية.transaction_date <= end_date
            )

            if transaction_type != "all":
                query = query.filter(حركة_نقدية.transaction_type == transaction_type)

            # استعلام سندات الصرف (المسحوبات) من المشتريات
            purchase_query = session.query(
                Purchase.id,
                Purchase.purchase_date.label('transaction_date'),
                func.cast('withdraw', String).label('transaction_type'),
                Purchase.amount_paid.label('amount'),
                func.cast(0.0, Float).label('balance_after'),
                func.cast("فاتورة مشتريات #" + func.cast(Purchase.id, String), String).label('reference'),
                func.cast("دفعة لفاتورة المورد " + Supplier.name, String).label('description'),
                func.cast(None, Integer).label('created_by'), # Set created_by to None
                Purchase.supplier_id.label('supplier_id'), # Added supplier_id
                func.cast(None, Integer).label('customer_id') # Added customer_id as None
            ).join(Supplier, Purchase.supplier_id == Supplier.id).filter(
                Purchase.purchase_date >= start_date,
                Purchase.purchase_date <= end_date,
                Purchase.amount_paid > 0
            )

            # استعلام سندات الصرف (المسحوبات) من سندات الصرف المباشرة
            receipt_out_query = session.query(
                Receipt.id,
                Receipt.issue_date.label('transaction_date'),
                func.cast('withdraw', String).label('transaction_type'),
                Receipt.amount_usd.label('amount'),
                func.cast(0.0, Float).label('balance_after'),
                func.cast("سند صرف #" + func.cast(Receipt.id, String), String).label('reference'),
                func.cast(
                    case(
                        (Receipt.supplier_id != None, "دفعة للمورد " + Supplier.name),
                        else_="سند صرف نقدي"
                    ),
                    String
                ).label('description'),
                func.cast(None, Integer).label('created_by'), # Set created_by to None
                Receipt.supplier_id.label('supplier_id'), # Added supplier_id
                func.cast(None, Integer).label('customer_id') # Added customer_id as None
            ).outerjoin(Supplier, Receipt.supplier_id == Supplier.id).filter(
                Receipt.issue_date >= start_date,
                Receipt.issue_date <= end_date,
                or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")
            )

            # استعلام سندات القبض (الإيداعات) من سندات القبض المباشرة
            receipt_in_query = session.query(
                Receipt.id,
                Receipt.issue_date.label('transaction_date'),
                func.cast('deposit', String).label('transaction_type'),
                Receipt.amount_usd.label('amount'),
                func.cast(0.0, Float).label('balance_after'),
                func.cast("سند قبض #" + func.cast(Receipt.id, String), String).label('reference'),
                func.cast(
                    case(
                        (Receipt.customer_id != None, "دفعة من العميل " + Customer.name),
                        else_="سند قبض نقدي"
                    ),
                    String
                ).label('description'),
                func.cast(None, Integer).label('created_by'), # Set created_by to None
                Receipt.customer_id.label('customer_id'), # Added customer_id
                func.cast(None, Integer).label('supplier_id') # Added supplier_id as None
            ).outerjoin(Customer, Receipt.customer_id == Customer.id).filter(
                Receipt.issue_date >= start_date,
                Receipt.issue_date <= end_date,
                or_(Receipt.receipt_type == "CashIn", Receipt.receipt_type == "قبض")
            )

            if transaction_type == "deposit":
                purchase_query = purchase_query.filter(False)  # لا تضمن سندات الصرف إذا كان البحث عن الإيداعات فقط
                receipt_out_query = receipt_out_query.filter(False)  # لا تضمن سندات الصرف إذا كان البحث عن الإيداعات فقط
            elif transaction_type == "withdraw":
                receipt_in_query = receipt_in_query.filter(False)  # لا تضمن سندات القبض إذا كان البحث عن المسحوبات فقط

            # ترتيب حسب التاريخ تنازلياً
            transactions = query.order_by(desc(حركة_نقدية.transaction_date)).all()

            # دمج السندات مع الحركات النقدية
            if transaction_type != "withdraw":
                # إضافة الإيداعات من سندات القبض
                receipt_in_transactions = receipt_in_query.all()
                for rt in receipt_in_transactions:
                    temp_transaction = type('TransactionLike', (), {
                        'id': f"receipt_in_{rt.id}",
                        'transaction_date': rt.transaction_date,
                        'transaction_type': rt.transaction_type,
                        'amount': rt.amount, # USD
                        'balance_after': rt.balance_after,
                        'reference': rt.reference,
                        'description': rt.description,
                        'created_by': rt.created_by,
                        'customer_id': rt.customer_id,
                        'supplier_id': None,
                        'currency': 'USD',
                        'original_amount': rt.amount, # USD amount as original
                        'exchange_rate': 1.0
                    })
                    transactions.append(temp_transaction)

            if transaction_type != "deposit":
                # إضافة المسحوبات من فواتير المشتريات
                purchase_transactions = purchase_query.all()
                for pt in purchase_transactions:
                    temp_transaction = type('TransactionLike', (), {
                        'id': f"purchase_{pt.id}",
                        'transaction_date': pt.transaction_date,
                        'transaction_type': pt.transaction_type,
                        'amount': pt.amount, # USD
                        'balance_after': pt.balance_after,
                        'reference': pt.reference,
                        'description': pt.description,
                        'created_by': pt.created_by,
                        'customer_id': None,
                        'supplier_id': pt.supplier_id,
                        'currency': 'USD',
                        'original_amount': pt.amount, # USD amount as original
                        'exchange_rate': 1.0
                    })
                    transactions.append(temp_transaction)

                # إضافة المسحوبات من سندات الصرف
                receipt_out_transactions = receipt_out_query.all()
                for rt in receipt_out_transactions:
                    temp_transaction = type('TransactionLike', (), {
                        'id': f"receipt_out_{rt.id}",
                        'transaction_date': rt.transaction_date,
                        'transaction_type': rt.transaction_type,
                        'amount': rt.amount, # USD
                        'balance_after': rt.balance_after,
                        'reference': rt.reference,
                        'description': rt.description,
                        'created_by': rt.created_by,
                        'customer_id': None,
                        'supplier_id': rt.supplier_id,
                        'currency': 'USD',
                        'original_amount': rt.amount, # USD amount as original
                        'exchange_rate': 1.0
                    })
                    transactions.append(temp_transaction)

            # إعادة ترتيب جميع الحركات حسب التاريخ
            transactions.sort(key=lambda x: x.transaction_date, reverse=True)

            # تعيين عدد الصفوف
            self.transactions_table.setRowCount(len(transactions))

            # متغيرات للرصيد المتراكم
            exchange_rate = 3.75  # سعر الصرف الافتراضي

            # ترتيب المعاملات حسب التاريخ (من الأقدم للأحدث) لحساب الرصيد المتراكم
            transactions_sorted = sorted(transactions, key=lambda x: x.transaction_date)

            # حساب الرصيد المتراكم لكل معاملة
            cumulative_balances = {}
            cumulative_balance_usd = 0.0
            cumulative_balance_sar = 0.0

            for transaction in transactions_sorted:
                if transaction.transaction_type == "deposit":
                    cumulative_balance_usd += transaction.amount
                    cumulative_balance_sar += transaction.amount * exchange_rate
                else:
                    cumulative_balance_usd -= transaction.amount
                    cumulative_balance_sar -= transaction.amount * exchange_rate

                cumulative_balances[transaction.id] = {
                    'usd': cumulative_balance_usd,
                    'sar': cumulative_balance_sar
                }

            # ملء بيانات الجدول (بالترتيب العكسي للعرض)
            for row, transaction in enumerate(transactions):
                # رقم العملية
                id_item = QTableWidgetItem(str(transaction.id))
                id_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 0, id_item)

                # التاريخ
                date_item = QTableWidgetItem(transaction.transaction_date.strftime("%Y-%m-%d %H:%M"))
                date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 1, date_item)

                # النوع
                type_text = ""
                debit_usd = 0.0
                credit_usd = 0.0
                debit_sar = 0.0
                credit_sar = 0.0

                if transaction.transaction_type == "deposit":
                    type_text = "إيداع/سند قبض"
                    debit_usd = transaction.amount  # مدين (موجب)
                    debit_sar = transaction.amount * exchange_rate
                else:
                    type_text = "سحب/سند صرف"
                    credit_usd = transaction.amount  # دائن (سالب)
                    credit_sar = transaction.amount * exchange_rate

                # الحصول على الرصيد المتراكم المحسوب مسبقاً
                balance_data = cumulative_balances.get(transaction.id, {'usd': 0.0, 'sar': 0.0})

                type_item = QTableWidgetItem(type_text)
                type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                # تلوين الخلية حسب النوع
                if transaction.transaction_type == "deposit":
                    type_item.setBackground(QColor(200, 250, 200))  # أخضر فاتح للإيداعات
                else:
                    type_item.setBackground(QColor(250, 200, 200))  # أحمر فاتح للمسحوبات

                self.transactions_table.setItem(row, 2, type_item)

                # مدين ($)
                debit_usd_item = QTableWidgetItem(f"{debit_usd:.2f}" if debit_usd > 0 else "")
                debit_usd_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 3, debit_usd_item)

                # دائن ($)
                credit_usd_item = QTableWidgetItem(f"{credit_usd:.2f}" if credit_usd > 0 else "")
                credit_usd_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 4, credit_usd_item)

                # الرصيد المتراكم ($)
                balance_usd_item = QTableWidgetItem(f"{balance_data['usd']:.2f}")
                balance_usd_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 5, balance_usd_item)

                # مدين (ريال)
                debit_sar_item = QTableWidgetItem(f"{debit_sar:.2f}" if debit_sar > 0 else "")
                debit_sar_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 6, debit_sar_item)

                # دائن (ريال)
                credit_sar_item = QTableWidgetItem(f"{credit_sar:.2f}" if credit_sar > 0 else "")
                credit_sar_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 7, credit_sar_item)

                # الرصيد المتراكم (ريال)
                balance_sar_item = QTableWidgetItem(f"{balance_data['sar']:.2f}")
                balance_sar_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.transactions_table.setItem(row, 8, balance_sar_item)

                # الوصف
                description_item = QTableWidgetItem(transaction.description or "")
                self.transactions_table.setItem(row, 9, description_item)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل حركات الصندوق: {str(e)}")

    def search_transactions(self):
        """البحث في حركات الصندوق حسب المعايير المحددة"""
        try:
            start_date = datetime.combine(self.start_date_edit.date().toPyDate(), datetime.min.time())
            end_date = datetime.combine(self.end_date_edit.date().toPyDate(), datetime.max.time())
            transaction_type = self.transaction_type_combo.currentData()

            with session_scope() as session:
                self.load_transactions(session, start_date, end_date, transaction_type)

                # تحديث ملخص الصندوق
                cash_box = session.query(صندوق_النقدية).first()
                if cash_box:
                    self.total_balance_label.setText(f"الرصيد الحالي: {cash_box.balance:.2f} $")

                # حساب إجمالي الإيداعات والمسحوبات للفترة المحددة
                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0

                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_date >= start_date,
                    حركة_نقدية.transaction_date <= end_date,
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0

                # حساب المسحوبات من فواتير المشتريات
                purchase_withdrawals = session.query(func.sum(Purchase.amount_paid)).filter(
                    Purchase.purchase_date >= start_date,
                    Purchase.purchase_date <= end_date,
                    Purchase.amount_paid > 0
                ).scalar() or 0

                # حساب الإيداعات من سندات القبض
                receipt_deposits = session.query(func.sum(Receipt.amount_usd)).filter(
                    Receipt.issue_date >= start_date,
                    Receipt.issue_date <= end_date,
                    or_(Receipt.receipt_type == "CashIn", Receipt.receipt_type == "قبض")
                ).scalar() or 0

                # حساب المسحوبات من سندات الصرف
                receipt_withdrawals = session.query(func.sum(Receipt.amount_usd)).filter(
                    Receipt.issue_date >= start_date,
                    Receipt.issue_date <= end_date,
                    or_(Receipt.receipt_type == "CashOut", Receipt.receipt_type == "صرف")
                ).scalar() or 0

                total_all_deposits = total_deposits + receipt_deposits
                total_all_withdrawals = total_withdrawals + purchase_withdrawals + receipt_withdrawals

                self.total_in_label.setText(f"إجمالي الإيداعات: {total_all_deposits:.2f} $")
                self.total_out_label.setText(f"إجمالي المسحوبات: {total_all_withdrawals:.2f} $")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")

    def add_deposit(self):
        """إضافة إيداع نقدي جديد"""
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية لإضافة إيداعات نقدية")
            return

        dialog = حوار_حركة_نقدية(transaction_type="deposit", user=self.user)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                amount_input = dialog.amount_spinbox.value()
                currency = dialog.currency_combo.currentData()
                exchange_rate = dialog.exchange_rate_spinbox.value() if currency == "SAR" else 1.0
                
                amount_usd = amount_input
                if currency == "SAR":
                    amount_usd = amount_input / exchange_rate

                transaction_date = datetime.combine(dialog.date_edit.date().toPyDate(), datetime.now().time())
                reference = dialog.reference_edit.text().strip()
                description_base = dialog.description_text.toPlainText().strip()
                
                description = f"{description_base} (المبلغ: {amount_input:.2f} {currency}"
                if currency == "SAR":
                    description += f" بسعر صرف: {exchange_rate:.4f} = {amount_usd:.2f} USD"
                description += ")"

                customer_id = None
                if dialog.customer_combo and dialog.customer_combo.currentData() is not None:
                    customer_id = dialog.customer_combo.currentData()
                    customer_name = dialog.customer_combo.currentText()
                    description += f" - من العميل: {customer_name}"


                with session_scope() as session:
                    cash_box = session.query(صندوق_النقدية).first()
                    if not cash_box:
                        cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                        session.add(cash_box)
                        session.flush()

                    new_balance = cash_box.balance + amount_usd

                    transaction = حركة_نقدية(
                        cash_box_id=cash_box.id,
                        transaction_type="deposit",
                        amount=amount_usd, # Store USD amount
                        balance_after=new_balance,
                        transaction_date=transaction_date,
                        description=description,
                        reference=reference,
                        created_by=self.user.id,
                        customer_id=customer_id,
                        currency=currency,
                        original_amount=amount_input,
                        exchange_rate=exchange_rate if currency == "SAR" else None
                    )
                    session.add(transaction)
                    cash_box.balance = new_balance
                    cash_box.last_updated = datetime.now()
                    session.commit()

                    QMessageBox.information(self, "نجح", f"تم إضافة إيداع بقيمة {amount_input:.2f} {currency} بنجاح")
                    self.load_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة الإيداع: {str(e)}")

    def add_withdrawal(self):
        """إضافة سحب نقدي جديد"""
        if not check_permission(self.user, "ادارة_صندوق_النقدية") and self.user.role != "admin":
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية لإضافة سحوبات نقدية")
            return

        dialog = حوار_حركة_نقدية(transaction_type="withdraw", user=self.user)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                amount_input = dialog.amount_spinbox.value()
                currency = dialog.currency_combo.currentData()
                exchange_rate = dialog.exchange_rate_spinbox.value() if currency == "SAR" else 1.0

                amount_usd = amount_input
                if currency == "SAR":
                    amount_usd = amount_input / exchange_rate
                
                transaction_date = datetime.combine(dialog.date_edit.date().toPyDate(), datetime.now().time())
                reference = dialog.reference_edit.text().strip()
                description_base = dialog.description_text.toPlainText().strip()

                description = f"{description_base} (المبلغ: {amount_input:.2f} {currency}"
                if currency == "SAR":
                    description += f" بسعر صرف: {exchange_rate:.4f} = {amount_usd:.2f} USD"
                description += ")"
                
                supplier_id = None
                if dialog.supplier_combo and dialog.supplier_combo.currentData() is not None:
                    supplier_id = dialog.supplier_combo.currentData()
                    supplier_name = dialog.supplier_combo.currentText()
                    description += f" - إلى المورد: {supplier_name}"

                with session_scope() as session:
                    cash_box = session.query(صندوق_النقدية).first()
                    if not cash_box:
                        QMessageBox.warning(self, "تنبيه", "لا يوجد صندوق نقدية")
                        return
                    
                    # التحقق من توفر الرصيد بالدولار الأمريكي
                    if cash_box.balance < amount_usd:
                        if not confirm_dialog(self, "تأكيد",
                                            f"الرصيد الحالي بالدولار ({cash_box.balance:.2f} $) أقل من المبلغ المطلوب سحبه ({amount_usd:.2f} $).\n"
                                            "هل تريد المتابعة؟"):
                            return

                    new_balance = cash_box.balance - amount_usd

                    transaction = حركة_نقدية(
                        cash_box_id=cash_box.id,
                        transaction_type="withdraw",
                        amount=amount_usd, # Store USD amount
                        balance_after=new_balance,
                        transaction_date=transaction_date,
                        description=description,
                        reference=reference,
                        created_by=self.user.id,
                        supplier_id=supplier_id,
                        currency=currency,
                        original_amount=amount_input,
                        exchange_rate=exchange_rate if currency == "SAR" else None
                    )
                    session.add(transaction)
                    cash_box.balance = new_balance
                    cash_box.last_updated = datetime.now()
                    session.commit()

                    QMessageBox.information(self, "نجح", f"تم إضافة سحب بقيمة {amount_input:.2f} {currency} بنجاح")
                    self.load_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة السحب: {str(e)}")

    def print_report(self):
        """طباعة تقرير صندوق النقدية"""
        try:
            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_cash_report_html()

            if not html_content:
                QMessageBox.warning(self, "تنبيه", "فشل في إنشاء محتوى التقرير")
                return

            # حفظ المحتوى في ملف مؤقت
            temp_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_cash_report.html")
            with open(temp_file, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # فتح الملف في المتصفح
            import webbrowser
            webbrowser.open('file://' + os.path.realpath(temp_file))

            QMessageBox.information(self, "نجح", "تم فتح التقرير في المتصفح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")

    def generate_cash_report_html(self):
        """إنشاء تقرير HTML لصندوق النقدية بالتنسيق المحسن"""
        try:
            # الحصول على بيانات الجدول
            rows = self.transactions_table.rowCount()
            cols = self.transactions_table.columnCount()

            # الحصول على قيم الملخص بشكل آمن
            try:
                balance_text = self.total_balance_label.text() if hasattr(self, 'total_balance_label') else "0.00 $"
                total_in_text = self.total_in_label.text() if hasattr(self, 'total_in_label') else "0.00 $"
                total_out_text = self.total_out_label.text() if hasattr(self, 'total_out_label') else "0.00 $"

                balance = balance_text.split(": ")[1] if ": " in balance_text else balance_text
                total_in = total_in_text.split(": ")[1] if ": " in total_in_text else total_in_text
                total_out = total_out_text.split(": ")[1] if ": " in total_out_text else total_out_text
            except Exception:
                balance = "0.00 $"
                total_in = "0.00 $"
                total_out = "0.00 $"

            # إعداد التواريخ ونطاق التقرير
            start_date_str = self.start_date_edit.date().toString("yyyy-MM-dd") if hasattr(self, 'start_date_edit') else "غير محدد"
            end_date_str = self.end_date_edit.date().toString("yyyy-MM-dd") if hasattr(self, 'end_date_edit') else "غير محدد"
            date_range = f"للفترة من {start_date_str} إلى {end_date_str}"

            # بناء محتوى الجدول
            table_content = ""
            table_content += "<table>"
            table_content += "<thead>"
            table_content += "<tr>"
            table_content += "<th>رقم العملية</th>"
            table_content += "<th>التاريخ</th>"
            table_content += "<th>النوع</th>"
            table_content += "<th>مدين ($)</th>"
            table_content += "<th>دائن ($)</th>"
            table_content += "<th>الرصيد المتراكم ($)</th>"
            table_content += "<th>مدين (ريال)</th>"
            table_content += "<th>دائن (ريال)</th>"
            table_content += "<th>الرصيد المتراكم (ريال)</th>"
            table_content += "<th>الوصف</th>"
            table_content += "</tr>"
            table_content += "</thead>"
            table_content += "<tbody>"

            # إضافة بيانات الجدول
            if rows == 0:
                # إضافة صف يوضح عدم وجود بيانات
                table_content += '<tr><td colspan="10" style="text-align: center; padding: 20px; color: #666;">لا توجد معاملات في الفترة المحددة</td></tr>'
            else:
                for row in range(rows):
                    # الحصول على نوع العملية
                    type_item = self.transactions_table.item(row, 2)
                    transaction_type = type_item.text() if type_item else ""

                    # تحديد لون الصف بناءً على نوع المعاملة
                    row_color = "#e8f7f2" if "إيداع" in transaction_type or "قبض" in transaction_type else "#fdf3f3"

                    table_content += f'<tr style="background-color: {row_color};">'

                    for col in range(cols):
                        item = self.transactions_table.item(row, col)
                        text = item.text() if item else ""
                        # تنظيف النص من الأحرف الخاصة
                        text = text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
                        table_content += f'<td>{text}</td>'

                    table_content += '</tr>'

            # إغلاق الجدول
            table_content += "</tbody>"
            table_content += "</table>"

            # بناء محتوى الملخص
            try:
                # تحديد فئة الرصيد (إيجابي أو سلبي)
                balance_value = float(balance.replace('$', '').replace(',', '').strip()) if balance else 0
                balance_class = "positive" if balance_value >= 0 else "negative"
            except (ValueError, TypeError):
                balance_class = "neutral"

            summary_content = ""
            summary_content += '<div class="summary-item">'
            summary_content += '<div class="summary-label">الرصيد الحالي</div>'
            summary_content += f'<div class="summary-value {balance_class}">{balance}</div>'
            summary_content += '</div>'
            summary_content += '<div class="summary-item">'
            summary_content += '<div class="summary-label">إجمالي الإيداعات</div>'
            summary_content += f'<div class="summary-value positive">{total_in}</div>'
            summary_content += '</div>'
            summary_content += '<div class="summary-item">'
            summary_content += '<div class="summary-label">إجمالي المسحوبات</div>'
            summary_content += f'<div class="summary-value negative">{total_out}</div>'
            summary_content += '</div>'

            # إنشاء HTML كامل باستخدام التنسيق المحسن
            html = self.generate_enhanced_html_template(
                title="تقرير صندوق النقدية",
                subtitle="تقرير صندوق النقدية",
                date_range=date_range,
                content=table_content,
                summary=summary_content
            )

            return html

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير HTML: {str(e)}")
            return ""

    def generate_enhanced_html_template(self, title, subtitle, date_range, content, summary=None, extra_info=None):
        """إنشاء قالب HTML محسن مع تنسيق متسق لجميع التقارير"""
        try:
            # الحصول على معلومات الشركة
            from db_session import session_scope
            from database import CompanyInfo

            company_name = "نظام إدارة مبيعات الألماس"
            company_logo = ""

            try:
                with session_scope() as session:
                    company_info = session.query(CompanyInfo).first()
                    if company_info:
                        if company_info.name:
                            company_name = company_info.name
                        if company_info.logo_path:
                            company_logo = company_info.logo_path
            except Exception:
                pass  # استخدام القيم الافتراضية

            # بناء محتوى HTML مع التنسيق المحسن
            html = "<!DOCTYPE html>"
            html += '<html dir="rtl" lang="ar">'
            html += '<head>'
            html += '<meta charset="UTF-8">'
            html += '<meta name="viewport" content="width=device-width, initial-scale=1.0">'
            html += f'<title>{title}</title>'
            html += '<style>'
            html += "@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');"
            html += "body { font-family: 'Tajawal', Arial, sans-serif; margin: 0; padding: 20px; direction: rtl; background-color: #f9f9f9; color: #333; }"
            html += ".container { max-width: 1200px; margin: 0 auto; background-color: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); }"
            html += ".header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }"
            html += ".logo { max-width: 150px; margin-bottom: 15px; }"
            html += ".title { font-size: 28px; font-weight: 700; color: #2c3e50; margin-bottom: 10px; }"
            html += ".subtitle { font-size: 20px; font-weight: 500; color: #3498db; margin: 10px 0; }"
            html += ".date-range { font-size: 16px; color: #7f8c8d; margin-top: 10px; }"
            html += ".info-box { margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-right: 4px solid #3498db; }"
            html += ".info-box h3 { margin-top: 0; color: #3498db; }"
            html += "table { width: 100%; border-collapse: collapse; margin: 25px 0; font-size: 16px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.05); border-radius: 5px; overflow: hidden; }"
            html += "th, td { padding: 12px 15px; text-align: right; }"
            html += "th { background-color: #3498db; color: white; font-weight: 500; text-transform: uppercase; letter-spacing: 1px; }"
            html += "tr:nth-child(even) { background-color: #f2f2f2; }"
            html += "tr:hover { background-color: #e9f7fe; }"
            html += "tfoot tr { background-color: #2c3e50; color: white; font-weight: bold; }"
            html += ".summary { display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 30px; background-color: #f8f9fa; padding: 20px; border-radius: 5px; }"
            html += ".summary-item { flex: 1; min-width: 200px; margin: 10px; padding: 15px; background-color: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); }"
            html += ".summary-label { font-size: 14px; color: #7f8c8d; margin-bottom: 5px; }"
            html += ".summary-value { font-size: 18px; font-weight: 700; color: #2c3e50; }"
            html += ".positive { color: #2ecc71; }"
            html += ".negative { color: #e74c3c; }"
            html += ".neutral { color: #3498db; }"
            html += ".footer { margin-top: 40px; text-align: center; font-size: 14px; color: #95a5a6; padding-top: 20px; border-top: 1px solid #eee; }"
            html += "@media print { body { background-color: white; margin: 0; padding: 0; } .container { box-shadow: none; max-width: 100%; padding: 15px; } table { page-break-inside: auto; } tr { page-break-inside: avoid; page-break-after: auto; } }"
            html += '</style>'
            html += '</head>'
            html += '<body>'
            html += '<div class="container">'
            html += '<div class="header">'
            if company_logo:
                html += f'<img src="{company_logo}" alt="Company Logo" class="logo">'
            html += f'<div class="title">{company_name}</div>'
            html += f'<div class="subtitle">{subtitle}</div>'
            html += f'<div class="date-range">{date_range}</div>'
            html += '</div>'
            if extra_info:
                html += f'<div class="info-box">{extra_info}</div>'
            html += content
            if summary:
                html += f'<div class="summary">{summary}</div>'
            html += '<div class="footer">'
            html += f'<p>تم إنشاء هذا التقرير بواسطة: {self.user.username}</p>'
            html += f'<p>تاريخ الطباعة: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>'
            html += '</div>'
            html += '</div>'
            html += '</body>'
            html += '</html>'

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء قالب HTML: {str(e)}")
            return ""
