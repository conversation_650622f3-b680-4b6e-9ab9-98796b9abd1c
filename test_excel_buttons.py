#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أزرار تصدير Excel في واجهة التقارير
"""

import sys
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PyQt6.QtCore import Qt

class TestExcelButtons(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("اختبار أزرار تصدير Excel")
        self.setGeometry(100, 100, 600, 400)

        layout = QVBoxLayout()

        # عنوان
        title = QLabel("اختبار أزرار تصدير Excel للتقارير")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)

        # قسم تقرير العملاء
        customer_section = QLabel("تقرير العملاء")
        customer_section.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
            }
        """)
        layout.addWidget(customer_section)

        customer_buttons = QHBoxLayout()
        
        # زر تصدير HTML للعملاء
        export_customer_html = QPushButton("تصدير HTML")
        export_customer_html.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        customer_buttons.addWidget(export_customer_html)

        # زر تصدير Excel للعملاء
        export_customer_excel = QPushButton("تصدير Excel")
        export_customer_excel.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
            QPushButton:pressed {
                background-color: #117a65;
            }
        """)
        customer_buttons.addWidget(export_customer_excel)

        layout.addLayout(customer_buttons)

        # قسم تقرير الموردين
        supplier_section = QLabel("تقرير الموردين")
        supplier_section.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
            }
        """)
        layout.addWidget(supplier_section)

        supplier_buttons = QHBoxLayout()
        
        # زر تصدير HTML للموردين
        export_supplier_html = QPushButton("تصدير HTML")
        export_supplier_html.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        supplier_buttons.addWidget(export_supplier_html)

        # زر تصدير Excel للموردين
        export_supplier_excel = QPushButton("تصدير Excel")
        export_supplier_excel.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
            QPushButton:pressed {
                background-color: #117a65;
            }
        """)
        supplier_buttons.addWidget(export_supplier_excel)

        layout.addLayout(supplier_buttons)

        # ملاحظة
        note = QLabel("ملاحظة: تم إضافة أزرار تصدير Excel بنجاح إلى تقارير العملاء والموردين")
        note.setAlignment(Qt.AlignmentFlag.AlignCenter)
        note.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #27ae60;
                padding: 15px;
                background-color: #d5f4e6;
                border-radius: 5px;
                border: 1px solid #27ae60;
                margin-top: 20px;
            }
        """)
        layout.addWidget(note)

        # ربط الأحداث
        export_customer_html.clicked.connect(lambda: self.show_message("تصدير HTML للعملاء"))
        export_customer_excel.clicked.connect(lambda: self.show_message("تصدير Excel للعملاء"))
        export_supplier_html.clicked.connect(lambda: self.show_message("تصدير HTML للموردين"))
        export_supplier_excel.clicked.connect(lambda: self.show_message("تصدير Excel للموردين"))

        self.setLayout(layout)

    def show_message(self, action):
        print(f"تم الضغط على: {action}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestExcelButtons()
    window.show()
    sys.exit(app.exec())
