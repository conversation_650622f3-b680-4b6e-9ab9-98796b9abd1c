<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العميل - اختبار</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            background-color: #f9f9f9;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 20px;
            font-weight: 500;
            color: #3498db;
            margin: 10px 0;
        }

        .date-range {
            font-size: 16px;
            color: #7f8c8d;
            margin-top: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 16px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            border-radius: 5px;
            overflow: hidden;
        }

        th, td {
            padding: 12px 15px;
            text-align: center;
        }

        th {
            background-color: #3498db;
            color: white;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tr:hover {
            background-color: #e9f7fe;
        }

        tfoot tr {
            background-color: #2c3e50;
            color: white;
            font-weight: bold;
        }

        .table-totals {
            background-color: #2c3e50 !important;
        }

        .summary {
            margin-top: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }

        .summary-row {
            margin-bottom: 15px;
            padding: 12px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .summary-item-compact {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .summary-label {
            font-size: 16px;
            color: #2c3e50;
            font-weight: 600;
            min-width: 120px;
        }

        .summary-value {
            font-size: 16px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }

        .summary-separator {
            color: #bdc3c7;
            font-weight: bold;
            margin: 0 5px;
        }

        .positive {
            color: #2ecc71;
        }

        .negative {
            color: #e74c3c;
        }

        .neutral {
            color: #3498db;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        /* Print styles for better pagination */
        @media print {
            body {
                background-color: white;
                padding: 0;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                padding: 20px;
            }

            /* Table styling for print */
            table {
                page-break-inside: auto;
            }

            /* Keep table header on each page */
            thead {
                display: table-header-group;
            }

            /* Table footer (totals) should appear only on last page */
            .table-totals {
                display: table-footer-group;
                page-break-inside: avoid;
                page-break-before: avoid;
            }

            /* Prevent rows from breaking across pages */
            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }

            /* Ensure summary appears on last page */
            .summary {
                page-break-inside: avoid;
                page-break-before: auto;
            }

            /* Ensure footer appears on last page */
            .footer {
                page-break-inside: avoid;
            }

            /* Force table footer to last page when table spans multiple pages */
            @page {
                margin: 2cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">نظام إدارة مبيعات الألماس</div>
            <div class="subtitle">تقرير حساب العميل: أحمد محمد</div>
            <div class="date-range">للفترة من 2024-01-01 إلى 2024-12-31</div>
        </div>

        <h3>تفاصيل المعاملات</h3>
        <table>
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>نوع الألماس</th>
                    <th>الوزن</th>
                    <th>سعر القيراط ($)</th>
                    <th>السعر بالريال للكمية</th>
                    <th>المبلغ المسدد ($)</th>
                    <th>المبلغ المسدد (ريال)</th>
                    <th>الرصيد ($)</th>
                    <th>الرصيد (ريال)</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2024-01-15</td>
                    <td>ألماس طبيعي</td>
                    <td>2.50</td>
                    <td>1000.00</td>
                    <td>9375.00</td>
                    <td>1500.00</td>
                    <td>5625.00</td>
                    <td>1000.00</td>
                    <td>3750.00</td>
                    <td>دفعة أولى</td>
                </tr>
                <tr>
                    <td>2024-02-20</td>
                    <td>ألماس صناعي</td>
                    <td>1.75</td>
                    <td>800.00</td>
                    <td>5250.00</td>
                    <td>800.00</td>
                    <td>3000.00</td>
                    <td>1600.00</td>
                    <td>6000.00</td>
                    <td>سداد كامل</td>
                </tr>
                <tr>
                    <td>2024-03-10</td>
                    <td>سند قبض</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>500.00</td>
                    <td>1875.00</td>
                    <td>1100.00</td>
                    <td>4125.00</td>
                    <td>دفعة على الحساب</td>
                </tr>
            </tbody>
            <tfoot class="table-totals">
                <tr style="background-color: #2c3e50; color: white; font-weight: bold;">
                    <th>الإجمالي</th>
                    <th></th>
                    <th>4.25</th>
                    <th>900.00</th>
                    <th>14625.00</th>
                    <th>2800.00</th>
                    <th>10500.00</th>
                    <th>1100.00</th>
                    <th>4125.00</th>
                    <th></th>
                </tr>
            </tfoot>
        </table>

        <div class="summary">
            <div class="summary-row">
                <div class="summary-item-compact">
                    <span class="summary-label">إجمالي الوزن:</span>
                    <span class="summary-value neutral">4.25 قيراط</span>
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-item-compact">
                    <span class="summary-label">إجمالي المستحق:</span>
                    <span class="summary-value negative">3900.00 $</span>
                    <span class="summary-separator">|</span>
                    <span class="summary-value negative">14625.00 ريال</span>
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-item-compact">
                    <span class="summary-label">إجمالي المسدد:</span>
                    <span class="summary-value positive">2800.00 $</span>
                    <span class="summary-separator">|</span>
                    <span class="summary-value positive">10500.00 ريال</span>
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-item-compact">
                    <span class="summary-label">إجمالي الرصيد:</span>
                    <span class="summary-value negative">1100.00 $</span>
                    <span class="summary-separator">|</span>
                    <span class="summary-value negative">4125.00 ريال</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة: المدير</p>
            <p>تاريخ الطباعة: 2024-12-19 14:30:00</p>
        </div>
    </div>
</body>
</html>
