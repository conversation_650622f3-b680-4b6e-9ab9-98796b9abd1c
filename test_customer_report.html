<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العميل - اختبار</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            direction: rtl;
            background-color: #f9f9f9;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 20px;
            font-weight: 500;
            color: #3498db;
            margin: 10px 0;
        }

        .date-range {
            font-size: 16px;
            color: #7f8c8d;
            margin-top: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 16px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            border-radius: 5px;
            overflow: hidden;
        }

        th, td {
            padding: 12px 15px;
            text-align: center;
        }

        th {
            background-color: #3498db;
            color: white;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tr:hover {
            background-color: #e9f7fe;
        }

        .summary {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-top: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }

        .summary-item {
            flex: 1;
            min-width: 200px;
            margin: 10px;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .summary-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
        }

        .positive {
            color: #2ecc71;
        }

        .negative {
            color: #e74c3c;
        }

        .neutral {
            color: #3498db;
        }

        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 14px;
            color: #95a5a6;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        /* Print styles for better pagination */
        @media print {
            body {
                background-color: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border-radius: 0;
                padding: 20px;
            }
            
            /* Ensure summary appears on last page */
            .summary {
                page-break-inside: avoid;
                page-break-before: auto;
            }
            
            /* Better page breaks */
            table {
                page-break-inside: auto;
            }
            
            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            thead {
                display: table-header-group;
            }
            
            /* Ensure footer appears on last page */
            .footer {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">نظام إدارة مبيعات الألماس</div>
            <div class="subtitle">تقرير حساب العميل: أحمد محمد</div>
            <div class="date-range">للفترة من 2024-01-01 إلى 2024-12-31</div>
        </div>

        <h3>تفاصيل المعاملات</h3>
        <table>
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>نوع الألماس</th>
                    <th>الوزن</th>
                    <th>سعر القيراط ($)</th>
                    <th>السعر بالريال للكمية</th>
                    <th>المبلغ المسدد ($)</th>
                    <th>المبلغ المسدد (ريال)</th>
                    <th>الرصيد ($)</th>
                    <th>الرصيد (ريال)</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2024-01-15</td>
                    <td>ألماس طبيعي</td>
                    <td>2.50</td>
                    <td>1000.00</td>
                    <td>9375.00</td>
                    <td>1500.00</td>
                    <td>5625.00</td>
                    <td>1000.00</td>
                    <td>3750.00</td>
                    <td>دفعة أولى</td>
                </tr>
                <tr>
                    <td>2024-02-20</td>
                    <td>ألماس صناعي</td>
                    <td>1.75</td>
                    <td>800.00</td>
                    <td>5250.00</td>
                    <td>800.00</td>
                    <td>3000.00</td>
                    <td>1600.00</td>
                    <td>6000.00</td>
                    <td>سداد كامل</td>
                </tr>
                <tr>
                    <td>2024-03-10</td>
                    <td>سند قبض</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>500.00</td>
                    <td>1875.00</td>
                    <td>1100.00</td>
                    <td>4125.00</td>
                    <td>دفعة على الحساب</td>
                </tr>
            </tbody>
        </table>

        <div class="summary">
            <div class="summary-item">
                <div class="summary-label">إجمالي الوزن</div>
                <div class="summary-value neutral">4.25 قيراط</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">إجمالي المستحق ($)</div>
                <div class="summary-value negative">3900.00</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">إجمالي المستحق (ريال)</div>
                <div class="summary-value negative">14625.00</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">إجمالي المسدد ($)</div>
                <div class="summary-value positive">2800.00</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">إجمالي المسدد (ريال)</div>
                <div class="summary-value positive">10500.00</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">إجمالي الرصيد ($)</div>
                <div class="summary-value negative">1100.00</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">إجمالي الرصيد (ريال)</div>
                <div class="summary-value negative">4125.00</div>
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة: المدير</p>
            <p>تاريخ الطباعة: 2024-12-19 14:30:00</p>
        </div>
    </div>
</body>
</html>
