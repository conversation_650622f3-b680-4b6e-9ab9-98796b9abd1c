from PyQt6.QtWidgets import (QMain<PERSON><PERSON>ow, QW<PERSON>t, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QGridLayout, QSpacerItem,
                            QSizePolicy, QMessageBox, QComboBox)
from PyQt6.QtGui import QFont, QIcon, QPixmap
from PyQt6.QtCore import Qt, QSize
from translations import get_translation as _, change_language
import app_state
from ui_utils import center_window

# Import screens
from settings_screen import SettingsScreen
from users_screen import UsersScreen
from categories_units_screen import CategoriesUnitsScreen

class DashboardWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # قائمة لتتبع النوافذ المفتوحة
        self.open_windows = []
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle(_("dashboard_title"))
        self.setGeometry(100, 100, 900, 600)
        self.setWindowIcon(QIcon('assets/diamond_icon.png'))

        # Obtener el usuario actual y la configuración desde el estado de la aplicación
        self.user = app_state.get_current_user()
        if not self.user:
            QMessageBox.critical(self, "خطأ", "لم يتم العثور على المستخدم الحالي")
            self.close()
            return

        # Obtener la configuración actual
        from database import Setting
        try:
            # Usar la configuración del estado de la aplicación
            setting = app_state.current_settings
            if setting:
                self.current_language = setting.language
            else:
                self.current_language = "ar"  # Arabic as default
        except Exception:
            self.current_language = "ar"  # If error, use Arabic as default

        # Set layout direction based on language
        if self.current_language == "ar":
            self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        # Create central widget and layout
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)

        # Header Section
        header_layout = QHBoxLayout()

        # Logo and title
        title_label = QLabel(_("app_title"))
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50;")

        # Language selector
        self.language_combo = QComboBox()
        self.language_combo.addItem(_("arabic"), "ar")
        self.language_combo.addItem(_("english"), "en")

        # Set current language in combo box
        index = self.language_combo.findData(self.current_language)
        if index >= 0:
            self.language_combo.setCurrentIndex(index)

        self.language_combo.currentIndexChanged.connect(self.on_language_changed)

        # User info
        user_info = QLabel(f"{_('welcome')} {self.user.username} | {self.user.role}")
        if self.current_language == "ar":
            user_info.setAlignment(Qt.AlignmentFlag.AlignRight)
        else:
            user_info.setAlignment(Qt.AlignmentFlag.AlignLeft)
        user_info.setStyleSheet("color: #7f8c8d;")

        # Add widgets to header layout based on language direction
        if self.current_language == "ar":
            header_layout.addWidget(title_label)
            header_layout.addStretch()
            header_layout.addWidget(self.language_combo)
            header_layout.addWidget(user_info)
        else:
            header_layout.addWidget(user_info)
            header_layout.addWidget(self.language_combo)
            header_layout.addStretch()
            header_layout.addWidget(title_label)

        # Menu Section
        menu_layout = QGridLayout()        # Define menu buttons
        self.buttons = {
            "sales": self.create_menu_button(_("sales"), "assets/sales_icon.png", "#3498db",
                                          self.open_sales_screen),
            "purchases": self.create_menu_button(_("purchases"), "assets/purchases_icon.png", "#2ecc71",
                                              self.open_purchases_screen),
            "customers": self.create_menu_button(_("customers"), "assets/customers_icon.png", "#9b59b6",
                                              self.open_customers_screen),
            "suppliers": self.create_menu_button(_("suppliers"), "assets/suppliers_icon.png", "#e74c3c",
                                             self.open_suppliers_screen),
            "vouchers": self.create_menu_button(_("vouchers"), "assets/vouchers_icon.png", "#f39c12",
                                             self.open_vouchers_screen),            "cash_box": self.create_menu_button("صندوق النقدية", "assets/cash_box_icon.png", "#27ae60",
                                             self.open_cash_box_screen),
            "categories_units": self.create_menu_button("الأصناف والوحدات", "assets/categories_icon.png", "#16a085",
                                                    self.open_categories_units_screen),
            "opening_balances": self.create_menu_button("الأرصدة الافتتاحية", "assets/opening_balances_icon.png", "#8e44ad",
                                                    self.open_opening_balances_screen),
            "reports": self.create_menu_button(_("reports"), "assets/reports_icon.png", "#1abc9c",
                                            self.open_reports_screen),
            "settings": self.create_menu_button(_("settings"), "assets/settings_icon.png", "#7f8c8d",
                                             self.open_settings_screen),
            "users": self.create_menu_button(_("users"), "assets/users_icon.png", "#34495e",
                                          self.open_users_screen),
            "logout": self.create_menu_button(_("logout"), "assets/logout_icon.png", "#95a5a6",
                                          self.logout)
        }        # Add buttons to grid layout
        menu_layout.addWidget(self.buttons["sales"], 0, 0)
        menu_layout.addWidget(self.buttons["purchases"], 0, 1)
        menu_layout.addWidget(self.buttons["customers"], 0, 2)
        menu_layout.addWidget(self.buttons["suppliers"], 1, 0)
        menu_layout.addWidget(self.buttons["vouchers"], 1, 1)
        menu_layout.addWidget(self.buttons["cash_box"], 1, 2)
        menu_layout.addWidget(self.buttons["categories_units"], 2, 0)
        menu_layout.addWidget(self.buttons["opening_balances"], 2, 1)
        menu_layout.addWidget(self.buttons["reports"], 2, 2)
        menu_layout.addWidget(self.buttons["settings"], 3, 0)
        menu_layout.addWidget(self.buttons["users"], 3, 1)
        menu_layout.addWidget(self.buttons["logout"], 3, 2)

        # Add all layouts to main layout
        main_layout.addLayout(header_layout)
        main_layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        main_layout.addLayout(menu_layout)
        main_layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

        # Footer
        footer_label = QLabel(_("copyright"))
        footer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        footer_label.setStyleSheet("color: #7f8c8d;")
        main_layout.addWidget(footer_label)

        # Set central widget
        self.setCentralWidget(central_widget)

        # Hide users button if user is not admin
        if self.user.role != "admin":
            self.buttons["users"].hide()

    def create_menu_button(self, text, icon_path, color, callback):
        button = QPushButton(text)
        button.setMinimumSize(200, 100)
        button.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))

        # Try to load icon if exists, otherwise use text only
        try:
            button.setIcon(QIcon(icon_path))
            button.setIconSize(QSize(32, 32))
        except:
            # Icon not found, use text only
            pass

        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: black;
                border: none;
                border-radius: 5px;
                padding: 15px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """)

        button.clicked.connect(callback)
        return button

    def darken_color(self, hex_color):
        # Simple function to darken a hex color for hover effect
        # Remove # from hex color
        hex_color = hex_color.lstrip('#')
        r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        # Darken by 20%
        factor = 0.8
        r = max(0, int(r * factor))
        g = max(0, int(g * factor))
        b = max(0, int(b * factor))
        return f"#{r:02x}{g:02x}{b:02x}"

    def open_sales_screen(self):
        from sales_screen import SalesScreen
        current_user = app_state.get_current_user()
        self.sales_screen = SalesScreen(current_user)
        center_window(self.sales_screen)
        self.sales_screen.show()
        # إضافة النافذة إلى قائمة النوافذ المفتوحة
        self.open_windows.append(self.sales_screen)

    def open_purchases_screen(self):
        from purchases_screen import PurchasesScreen
        current_user = app_state.get_current_user()
        self.purchases_screen = PurchasesScreen(current_user)
        center_window(self.purchases_screen)
        self.purchases_screen.show()
        # إضافة النافذة إلى قائمة النوافذ المفتوحة
        self.open_windows.append(self.purchases_screen)

    def open_customers_screen(self):
        from customers_screen import CustomersScreen
        current_user = app_state.get_current_user()
        self.customers_screen = CustomersScreen(current_user)
        center_window(self.customers_screen)
        self.customers_screen.show()
        # إضافة النافذة إلى قائمة النوافذ المفتوحة
        self.open_windows.append(self.customers_screen)

    def open_suppliers_screen(self):
        from suppliers_screen import SuppliersScreen
        current_user = app_state.get_current_user()
        self.suppliers_screen = SuppliersScreen(current_user)
        center_window(self.suppliers_screen)
        self.suppliers_screen.show()
        # إضافة النافذة إلى قائمة النوافذ المفتوحة
        self.open_windows.append(self.suppliers_screen)

    def open_vouchers_screen(self):
        from vouchers_screen import VouchersScreen
        current_user = app_state.get_current_user()
        self.vouchers_screen = VouchersScreen(current_user)
        center_window(self.vouchers_screen)
        self.vouchers_screen.show()
        # إضافة النافذة إلى قائمة النوافذ المفتوحة
        self.open_windows.append(self.vouchers_screen)

    def open_reports_screen(self):
        from reports_screen import ReportsScreen
        current_user = app_state.get_current_user()
        self.reports_screen = ReportsScreen(current_user)
        center_window(self.reports_screen)
        self.reports_screen.show()
        # إضافة النافذة إلى قائمة النوافذ المفتوحة
        self.open_windows.append(self.reports_screen)

    def open_settings_screen(self):
        try:
            self.settings_screen = SettingsScreen()
            center_window(self.settings_screen)
            self.settings_screen.show()
            # إضافة النافذة إلى قائمة النوافذ المفتوحة
            self.open_windows.append(self.settings_screen)
        except Exception as e:
            print(f"Error opening settings screen: {e}")
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء فتح شاشة الإعدادات. يرجى المحاولة مرة أخرى.")

    def open_users_screen(self):
        try:
            # Verificar si el usuario es administrador
            current_user = app_state.get_current_user()

            if not current_user:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على المستخدم الحالي")
                return

            if current_user.role != "admin":
                QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية للوصول إلى إدارة المستخدمين")
                return

            # Crear y mostrar la pantalla de usuarios
            self.users_screen = UsersScreen()
            center_window(self.users_screen)
            self.users_screen.show()
            # إضافة النافذة إلى قائمة النوافذ المفتوحة
            self.open_windows.append(self.users_screen)
        except Exception as e:
            from logger import log_error
            log_error(f"خطأ في فتح شاشة المستخدمين: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع في البرنامج. يرجى مراجعة سجل الأخطاء للحصول على مزيد من المعلومات")

    def open_categories_units_screen(self):
        try:
            # Crear y mostrar la pantalla de categorías y unidades
            self.categories_units_screen = CategoriesUnitsScreen()

            # Conectar la señal de actualización de datos
            self.categories_units_screen.data_updated.connect(self.on_categories_units_updated)

            # لا نغير حجم النافذة - ستستخدم الحجم المحدد في الكلاس (1000x600)
            # center_window سيتم استدعاؤه من داخل الكلاس نفسه

            # Mostrar la ventana
            self.categories_units_screen.show()
            # إضافة النافذة إلى قائمة النوافذ المفتوحة
            self.open_windows.append(self.categories_units_screen)
        except Exception as e:
            from logger import log_error
            log_error(f"خطأ في فتح شاشة الأصناف والوحدات: {str(e)}", e)

    def open_opening_balances_screen(self):
        try:
            from opening_balances_screen import OpeningBalancesScreen
            # إنشاء وعرض شاشة الأرصدة الافتتاحية
            self.opening_balances_screen = OpeningBalancesScreen()

            # ربط إشارة تحديث البيانات
            self.opening_balances_screen.data_updated.connect(self.on_opening_balances_updated)

            # عرض النافذة
            self.opening_balances_screen.show()
            # إضافة النافذة إلى قائمة النوافذ المفتوحة
            self.open_windows.append(self.opening_balances_screen)
        except Exception as e:
            from logger import log_error
            log_error(f"خطأ في فتح شاشة الأرصدة الافتتاحية: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح شاشة الأرصدة الافتتاحية: {str(e)}")

    def on_categories_units_updated(self):
        """
        تحديث البيانات في الشاشات الأخرى عند تغيير الأصناف أو الوحدات
        """
        # يمكن تنفيذ أي إجراءات تحديث هنا إذا لزم الأمر
        pass

    def on_opening_balances_updated(self):
        """
        تحديث البيانات في الشاشات الأخرى عند تغيير الأرصدة الافتتاحية
        """
        # يمكن تنفيذ أي إجراءات تحديث هنا إذا لزم الأمر
        pass

    def on_language_changed(self, index):
        """
        Maneja el cambio de idioma
        """
        # Obtener el código de idioma seleccionado
        language_code = self.language_combo.itemData(index)

        # Si el idioma seleccionado es diferente al actual
        if language_code != self.current_language:
            # Cambiar el idioma en la base de datos
            if change_language(language_code):
                # Mostrar mensaje de éxito
                QMessageBox.information(
                    self,
                    _("language_changed"),
                    _("restart_required")
                )

                # Actualizar el idioma الحالي
                self.current_language = language_code

    def close_all_windows(self):
        """إغلاق جميع النوافذ المفتوحة مع التحقق من التغييرات غير المحفوظة"""
        unsaved_windows = []
        
        # التحقق من النوافذ التي تحتوي على تغييرات غير محفوظة
        for window in self.open_windows:
            if hasattr(window, 'has_unsaved_changes') and window.has_unsaved_changes():
                unsaved_windows.append(window)
        
        # إذا كانت هناك تغييرات غير محفوظة، اسأل المستخدم
        if unsaved_windows:
            reply = QMessageBox.question(
                self,
                "تغييرات غير محفوظة",
                f"هناك {len(unsaved_windows)} نافذة تحتوي على تغييرات غير محفوظة.\nهل تريد إغلاق جميع النوافذ بدون حفظ؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                return False
        
        # إغلاق جميع النوافذ
        for window in self.open_windows[:]:
            try:
                window.close()
                self.open_windows.remove(window)
            except:
                pass
        
        return True

    def closeEvent(self, event):
        """معالجة إغلاق النافذة الرئيسية"""
        if self.close_all_windows():
            event.accept()
        else:
            event.ignore()

    def logout(self):
        # إغلاق جميع النوافذ المفتوحة أولاً
        if not self.close_all_windows():
            return  # إذا ألغى المستخدم العملية
            
        # Close the dashboard window and show login window
        from login_screen import LoginWindow
        self.login_window = LoginWindow()
        center_window(self.login_window)
        self.login_window.show()
        self.close()

    def open_cash_box_screen(self):
        try:
            # Check if user has permission
            current_user = app_state.get_current_user()

            if not current_user:
                QMessageBox.critical(self, "خطأ", "لم يتم العثور على المستخدم الحالي")
                return

            # Verify user permission for cash box
            from permissions import check_permission
            has_view_permission = check_permission(current_user, "عرض_صندوق_النقدية")
            if not has_view_permission and current_user.role != "admin":
                QMessageBox.warning(self, "تحذير", "ليس لديك صلاحية للوصول إلى صندوق النقدية")
                return

            # Import and show cash box screen
            from cash_box_screen import شاشة_صندوق_النقدية
            self.cash_box_screen = شاشة_صندوق_النقدية(current_user)
            center_window(self.cash_box_screen)
            self.cash_box_screen.show()
            # إضافة النافذة إلى قائمة النوافذ المفتوحة
            self.open_windows.append(self.cash_box_screen)
        except Exception as e:
            from logger import log_error
            log_error(f"خطأ في فتح شاشة صندوق النقدية: {str(e)}", e)
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع. يرجى مراجعة سجل الأخطاء للحصول على مزيد من المعلومات")